import React, { useCallback, useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, View, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useFocusEffect } from '@react-navigation/native';
import { isTablet } from 'react-native-device-info';
import { RefreshControl } from 'react-native-gesture-handler';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../redux/hooks';
import useProducts from '../../hooks/useProducts';
import { getNewProducts, getCategory, getRestockedProducts } from '../../redux/apis/product';
import { getTenantDetails, walletDetail } from '../../redux/apis/payment';
import { getDeals } from '../../redux/apis/deals';
import { clearDeals } from '../../redux/features/deals-slice';
import { setUserCurrentLocation } from '../../redux/features/tracking-slice';
import { clearNewProducts, clearRestockedProducts, loaderNewProducts, loaderRestockedProducts } from '../../redux/features/newProductsOnline-slice';
import { setStandLoader } from '../../redux/features/stands-slice';
import { getCurrentShiftActivity, salesPersonWorkShift } from '../../redux/apis/tracking';
import { getBranchId, getCustomerUserRoleId, getPriceListId, getSalesPersonId, getSalesPersonRoleId, getUserSetting, getUserType } from '../../redux/selectors';
import { getStandList } from '../../redux/apis/stands';
import { AllDealsHorizontal } from '../../components/mobile/deals';
import {
	BrowseCategories,
	NavbarContainer,
	NewProducts,
	SearchBarButton,
	SelectCustomer
} from '../../components/mobile';
import { ShiftControl } from '../../components/mobile/tracking';
import { Banners, GetExtraBanner } from '../../components/mobile/home';
import { SEARCH_TYPES, STATUS, USER_TYPES } from '../../constants';
import styles from './styles';
import { getCurrentLocation } from '../../utils/location';
import LocationTracking from '../../utils/locationTracking';
import { colors } from '../../utils/theme';
import { HomeRewardButton } from '../../components/mobile/rewards';
import { checkBadRequest } from '../../utils/helpers';
import { getCustomerDetails } from '../../redux/apis/customer';
import useRewardProgram from '../../hooks/useRewardProgram';
import { RestockProducts } from '../../components/mobile/home/<USER>';

const Home = () => {
	const dispatch = useDispatch();
	const { t, i18n } = useTranslation();
	const userType = useAppSelector(getUserType);
	const branchId = useAppSelector(getBranchId);
	const priceListId = useAppSelector(getPriceListId);
	const salesPersonRoleId = useAppSelector(getSalesPersonRoleId);
	const userSetting = useAppSelector(getUserSetting);
	const salesPersonId = useAppSelector(getSalesPersonId);
	const customerUserRoleId = useAppSelector(getCustomerUserRoleId);
	const currentRole = useAppSelector(state => state.auth.currentRole);
	const masterSettings = useAppSelector(state => state.setting.masterSettings);
	// const deals2 = useAppSelector(state => state.deal.deals2);
	const dealArrayForMobile = useAppSelector(state => state.deal.dealArrayForMobile);
	const { activeActivity } = useAppSelector(state => state.tracking);

	// To get payment enable flag
	const tenantDetails = useAppSelector(state => state.payment.tenantDetails);
	const paymentService = tenantDetails?.services?.filter((e: { key: string }) => e?.key === 'payments');
	const isPaymentEnable = paymentService ? paymentService[0]?.permission?.edit : false;

	const [refreshing, setRefreshing] = useState(false);
	useProducts();
	const { allowToGetMemeberDetail, fetchRewardData, dailyAccessRewardData } = useRewardProgram();
	useEffect(() => {
		if (allowToGetMemeberDetail) {
			fetchRewardData();
		}
	}, [allowToGetMemeberDetail]);

	useEffect(() => {
		if (userType === USER_TYPES.SALES_APP && isTablet() === false) {
			LocationTracking.init(currentRole?.tenant_id?._id, currentRole._id, currentRole?.tenant_id?.country.timezone);
			updateCurrentLocation();
		}
	}, []);

	useFocusEffect(useCallback(() => {
		dispatch(getTenantDetails());
	}, []));

	useEffect(() => {
		if (userType === USER_TYPES.SALES_APP && isTablet() === false && isPaymentEnable) {
			getWalletDetails();
		}
	}, [isPaymentEnable]);

	useEffect(() => {
		dispatch(clearNewProducts());
		dispatch(clearRestockedProducts());
		dispatch(clearDeals());
		loadNewProducts();
		loadRestockProducts();
		loadDeals();
	}, [priceListId, userSetting?.out_of_stock]);

	const getWalletDetails = async () => {
		const requestBody: any = {
			salesPersonId: salesPersonId
		};
		await dispatch(walletDetail(requestBody));
	};

	const loadDeals = () => {
		// Call api when priceListId available
		if (priceListId) {
			let requestBody: any = {
				tenantId: currentRole?.tenant_id?._id,
				priceId: priceListId
			};

			if (salesPersonRoleId) {
				requestBody.salesPersonUserId = salesPersonRoleId;
			}
			if (masterSettings && masterSettings?.hide_out_of_stock_product) {
				requestBody.hideOutOfStock = true;
				requestBody.branchId = branchId;
			}
			dispatch(getDeals(requestBody));
		}
	};

	const updateCurrentLocation = async () => {
		if (userType === USER_TYPES.SALES_APP) {
			const location: any = await getCurrentLocation();
			if (location !== null) {
				const { coords } = location;
				dispatch(setUserCurrentLocation(coords));
			}
		}
	};

	const loadBrowseCategories = async () => {
		let requestBody: any = {
			tenantId: currentRole?.tenant_id?._id,
			priceListId
		};
		if (!userSetting?.out_of_stock?.visible) {
			requestBody.hideOutOfStock = !userSetting?.out_of_stock.visible;
			requestBody.branchId = branchId;
		}
		await dispatch(getCategory(requestBody));
	};

	const loadNewProducts = () => {
		if (priceListId) {
			let requestBody: any = {
				priceListId,
				tenantId: currentRole?.tenant_id?._id,
				page: 1,
				perPage: 10,
				searchType: SEARCH_TYPES.FILTER,
				isPrimaryLanguage: i18n.language === 'en',
				filters: { productType: ['NEW_PRODUCTS'] },
				hideOutOfStock: userType === USER_TYPES.CUSTOMER_APP ? true : !userSetting?.out_of_stock?.visible,
				branchId: branchId
			};
			if (salesPersonRoleId) {
				requestBody.salesPersonUserRoleId = salesPersonRoleId;
			}
			dispatch(getNewProducts(requestBody));
		} else {
			dispatch(loaderNewProducts());
		}
	};
	const loadRestockProducts = () => {
		if (priceListId) {
			let requestBody: any = {
				priceListId,
				tenantId: currentRole?.tenant_id?._id,
				page: 1,
				perPage: 10,
				searchType: SEARCH_TYPES.FILTER,
				isPrimaryLanguage: i18n.language === 'en',
				filters: { productType: ['RESTOCKED_PRODUCTS'] },
				hideOutOfStock: userType === USER_TYPES.CUSTOMER_APP ? true : !userSetting?.out_of_stock?.visible,
				branchId: branchId
			};
			if (salesPersonRoleId) {
				requestBody.salesPersonUserRoleId = salesPersonRoleId;
			}
			dispatch(getRestockedProducts(requestBody));
		} else {
			dispatch(loaderRestockedProducts());
		}
	};

	const getSalesPersonTrackingData = async () => {
		if (userType === USER_TYPES.SALES_APP && isTablet() === false) {
			await dispatch(getCurrentShiftActivity()); // Current ongoing shift and current ongoing activity in shift
			await dispatch(salesPersonWorkShift()); // Get shift hours configuration
			// LocationTracking.init(currentRole?.tenant_id?._id, currentRole._id);
		}
	};

	const getStandsList = async () => {
		if (userType === USER_TYPES.SALES_APP && isTablet() === false) {
			dispatch(setStandLoader(true));
			const requestBody = {
				tenantId: currentRole?.tenant_id?._id,
				type: STATUS.ALL,
				standStatus: [STATUS.ACTIVE, STATUS.PENDING],
				customerUserRoleId: customerUserRoleId,
				visitId: activeActivity?._id
			};
			const response = await dispatch(getStandList(requestBody));
			dispatch(setStandLoader(false));
			if (response.error) {
				Alert.alert(t(checkBadRequest(response.payload)));
			}
		}
	};

	const getCustomerData = async () => {
		if (userType === USER_TYPES.CUSTOMER_APP) {
			await dispatch(getCustomerDetails({ userRoleId: customerUserRoleId }));
		}
	};

	const onRefresh = useCallback(async () => {
		dispatch(clearNewProducts());
		dispatch(clearRestockedProducts());
		dispatch(clearDeals());
		setRefreshing(true);
		loadNewProducts();
		loadRestockProducts();
		loadBrowseCategories();
		loadDeals();
		getSalesPersonTrackingData();
		setRefreshing(false);
		dispatch(getTenantDetails());
		getStandsList();
		getCustomerData();
		if (allowToGetMemeberDetail) {
			fetchRewardData();
		}
	}, [priceListId, salesPersonRoleId, masterSettings, userSetting?.out_of_stock]);

	return (
		<SafeAreaView style={styles.safeAreaView}>
			<ScrollView
				contentContainerStyle={styles.container}
				showsVerticalScrollIndicator={false}
				refreshControl={
					<RefreshControl
						refreshing={refreshing}
						onRefresh={onRefresh}
						tintColor={colors.grey400}
					/>
				}
			>
				<View style={styles.subContainer}>
					<NavbarContainer>
						{
							userType !== USER_TYPES.CUSTOMER_APP ? <SelectCustomer /> : <SearchBarButton containerStyle={styles.searchBar} />
						}
					</NavbarContainer>
					<Banners tenantId={currentRole?.tenant_id?._id} />
					{(userType === USER_TYPES.CUSTOMER_APP || userType === USER_TYPES.SALES_APP) && <GetExtraBanner tenantId={currentRole?.tenant_id?._id} />}
					<ShiftControl />
					{userType === USER_TYPES.CUSTOMER_APP &&
						<HomeRewardButton {...{ dailyAccessRewardData }} />
					}
					<NewProducts />
					<RestockProducts />
					<BrowseCategories />
					{/* <DealCarouselMobileNormal /> */}
					{/* <DealCarouselMobileSmall /> */}
					{/* <DealCarouselMobile /> */}

					{
						dealArrayForMobile.length > 0 && dealArrayForMobile.map((item: any, index: number) => {
							const key = `${item._id}${index}`;
							return <AllDealsHorizontal key={key} deals={item} />;
						})
					}
					{/* {deals2.length > 0 && <DealsHorizontal />} */}
				</View>
			</ScrollView>
		</SafeAreaView>
	);
};

export default Home;
