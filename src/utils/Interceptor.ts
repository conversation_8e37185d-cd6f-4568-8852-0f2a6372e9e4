import { Alert, Platform } from 'react-native';
import {
	getBuildNumber,
	getUniqueId,
	getVersion,
	isTablet
} from 'react-native-device-info';
import httpService from '../redux/apis/httpService';
import { AppLogout, updateAccessToken } from '../redux/features/auth-slice';
import { setAutoLogout } from '../redux/features/common-slice';
import { logFirebaseCustomEvent } from './firebase';
import { customEvents } from '../constants';
import i18n from '../locales/i18n';

const interceptor = async (store: any) => {
	httpService.interceptors.request.use(
		async (config: any) => {
			const auth = store.getState().auth;
			const accessToken = auth.token.accessToken;
			const refreshToken = auth.token.refreshToken;
			const userRoleId = auth.currentRole?._id;
			const uniqueId = await getUniqueId();
			const currentVersion = getVersion();
			const currentBuild = getBuildNumber();
			config.headers.common.deviceaccesstype = isTablet() ? 'TABLET' : 'MOBILE';
			config.headers.common.devicetoken = uniqueId; // Set device token header
			config.headers.common.devicetype = Platform.OS.toUpperCase(); // Set device type header
			config.headers.common.version = currentVersion; // Set version header
			config.headers.common.build = currentBuild; // Set build number header
			config.headers.common['Cache-Control'] = 'no-cache';

			const unAuthenticatedAPIs = [
				'common/get-countries',
				'auth/app-sign-in',
				'auth/verify-auth-otp'
			];

			if (accessToken && !unAuthenticatedAPIs.includes(config.url)) {
				// Adding authorization header
				config.headers.common.Authorization = accessToken;
				// console.log('request of', config.url, accessToken);
			}

			if (refreshToken && !unAuthenticatedAPIs.includes(config.url)) {
				// Adding refresh token header
				config.headers.common.refreshToken = refreshToken;
			}

			if (userRoleId && !unAuthenticatedAPIs.includes(config.url)) {
				// Adding userroleid header
				config.headers.common.userroleid = userRoleId;
			}
			// console.log('accessToken', accessToken);
			// console.log('refreshToken', refreshToken);
			// console.log('userroleid', userRoleId);
			// console.log('config', JSON.stringify(config));
			console.log('Starting Request', JSON.stringify(config, null, 2));
			return config;
		},
		(error) => Promise.reject(error)
	);

	httpService.interceptors.response.use(
		(next: any) => {
			// console.log("next.headers['refreshed-access-token']", next.headers['refreshed-access-token']);
			// console.log('next', next);
			if (next.headers['refreshed-access-token']) {
				// console.log('api', next.config.url);
				// console.log('refreshed access token', next.headers['refreshed-access-token']);
				// console.log('requested auth token', next.config.headers.Authorization);
				// console.log(next.headers['refreshed-access-token'] === next.config.headers.Authorization, next.config.headers.Authorization);
				// console.log('token refreshed', next.headers['refreshed-access-token']);
				store.dispatch(
					updateAccessToken(next.headers['refreshed-access-token'])
				);
			}
			return Promise.resolve(next);
		},
		(error) => {
			if (
				error.response.status === 401 &&
				error.response.data.message !== 'mobile_not_exists'
			) {
				const auth = store.getState().auth;
				if (
					auth?.isLoggedIn &&
					error.response.data.message !== 'invalid_session'
				) {
					Alert.alert(i18n.t(error.response.data.message));
				}
				store.dispatch(AppLogout());
				if (error.response.data.message === 'invalid_session') {
					store.dispatch(setAutoLogout(true));
				}
				logFirebaseCustomEvent(customEvents.UNAUTHORIZED); // Log unauthorized firebase analytics custom event
			}
			return Promise.reject(error);
		}
		// You can handle error here and trigger warning message without get in the code inside
	);
};

export default {
	interceptor
};
